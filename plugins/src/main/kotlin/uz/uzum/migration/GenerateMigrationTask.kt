package uz.uzum.migration

import org.gradle.api.GradleException
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.TaskAction
import org.slf4j.LoggerFactory
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

abstract class GenerateMigrationTask : org.gradle.api.DefaultTask() {
    private val logger = LoggerFactory.getLogger(GenerateMigrationTask::class.java)

    @get:Input
    abstract val migrationName: Property<String>

    init {
        migrationName.convention("")
    }

    @TaskAction
    fun generate() {
        validateInput()
        val (yearMonthDir, migrationFile) = createMigrationFile()
        val masterChangelog = updateMasterChangelog(yearMonthDir)
        writeTemplateToFile(migrationFile)
        commitToGit(migrationFile, masterChangelog)
    }

    private fun validateInput() {
        if (migrationName.get().isBlank()) {
            throw GradleException("Please specify -PmigrationName=your_migration_name")
        }
    }

    private fun createMigrationFile(): Pair<File, File> {
        val timestamp = LocalDateTime.now()
        val yearMonth = timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM"))

        val changelogDir = project.file("user-notification-manager-impl/src/main/resources/db/changelog")
        val yearMonthDir = File(changelogDir, yearMonth)
        yearMonthDir.mkdirs()

        val formattedTimestamp = timestamp.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        val migrationFileName = "${formattedTimestamp}_${migrationName.get()}"
        val migrationFile = File(yearMonthDir, "$migrationFileName.yml")
        migrationFile.createNewFile()

        return Pair(yearMonthDir, migrationFile)
    }

    private fun writeTemplateToFile(migrationFile: File) {
        val gitUsername = getGitUsername()
        val migrationFileName = migrationFile.nameWithoutExtension

        migrationFile.writeText(generateTemplate(migrationFileName, gitUsername))
    }

    private fun updateMasterChangelog(yearMonthDir: File): File {
        val changelogDir = yearMonthDir.parentFile
        val masterChangelog = File(changelogDir, "db.changelog-master.yml")
        val yearMonth = yearMonthDir.name

        if (!masterChangelog.exists()) {
            masterChangelog.writeText(
                """
                databaseChangeLog:
                  - includeAll:
                      path: $yearMonth
                      relativeToChangelogFile: true
                """.trimIndent() + "\n",
            )
            return masterChangelog
        }

        val content = masterChangelog.readText()

        if (hasExistingIncludeAll(content, yearMonth)) {
            logger.info("Directory $yearMonth is already included in master changelog, skipping update")
            return masterChangelog
        }

        val yamlMapper = org.yaml.snakeyaml.Yaml()
        val data = yamlMapper.load<Map<String, Any>>(content)

        @Suppress("UNCHECKED_CAST")
        val changeLog = data["databaseChangeLog"] as MutableList<Map<String, Any>>

        changeLog.add(
            mapOf(
                "includeAll" to
                        mapOf(
                            "path" to yearMonth,
                            "relativeToChangelogFile" to true,
                        ),
            ),
        )

        masterChangelog.writeText(yamlMapper.dump(mapOf("databaseChangeLog" to changeLog)))
        return masterChangelog
    }

    private fun hasExistingIncludeAll(
        content: String,
        yearMonth: String,
    ): Boolean {
        val yamlMapper = org.yaml.snakeyaml.Yaml()
        val data = yamlMapper.load<Map<String, Any>>(content)

        @Suppress("UNCHECKED_CAST")
        val changeLog = data["databaseChangeLog"] as List<Map<String, Any>>

        return changeLog.any { change ->
            val includeAll = change["includeAll"] as? Map<*, *>
            includeAll?.get("path") == yearMonth
        }
    }

    private fun commitToGit(
        migrationFile: File,
        masterChangelog: File,
    ) {
        logger.info("Created migration file: ${migrationFile.absolutePath}")
        logger.info("Updated master changelog: ${masterChangelog.absolutePath}")

        gitAdd(migrationFile)
        gitAdd(masterChangelog)
    }

    private fun getGitUsername(): String =
        try {
            val process =
                ProcessBuilder("git", "config", "user.name")
                    .redirectOutput(ProcessBuilder.Redirect.PIPE)
                    .redirectError(ProcessBuilder.Redirect.PIPE)
                    .start()

            process.waitFor()
            process.inputStream.bufferedReader()
                .readText()
                .trim()
                .takeIf { it.isNotEmpty() }
                ?: System.getProperty("user.name")
        } catch (e: Exception) {
            System.getProperty("user.name")
        }

    private fun gitAdd(file: File) {
        try {
            val process =
                ProcessBuilder("git", "add", file.absolutePath)
                    .redirectOutput(ProcessBuilder.Redirect.PIPE)
                    .redirectError(ProcessBuilder.Redirect.PIPE)
                    .start()

            if (process.waitFor() == 0) {
                logger.info("Added ${file.name} to git staging area")
            } else {
                logger.warn(
                    "Failed to add ${file.name} to git: ${
                        process.errorStream.bufferedReader().readText()
                    }",
                )
            }
        } catch (e: Exception) {
            logger.warn("Failed to execute git add command: ${e.message}")
        }
    }

    private fun generateTemplate(
        migrationFileName: String,
        gitUsername: String,
    ): String =
        """
        databaseChangeLog:
            - changeSet:
                id: $migrationFileName
                author: $gitUsername

                preConditions:
                - onFail: MARK_RAN
                
            # changes:
                # Template for table creation
                # - createTable:
                #     tableName: your_table
                #     columns:
                #       - column:
                #           name: id
                #           type: bigint
                #           autoIncrement: true
                #           constraints:
                #             primaryKey: true
                #             nullable: false
        """.trimIndent()
}
