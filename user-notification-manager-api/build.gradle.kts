plugins {
    alias(libs.plugins.openapi)
    `maven-publish`
    `java-library`
}

dependencies {
    api(libs.spring.webmvc)
    api(libs.jakarta.validation.api)
    api(libs.jakarta.annotation.api)
    api(libs.jakarta.servlet.api)
    api(libs.jackson.databind)
    api(libs.swagger.annotations)
}

repositories {
    mavenCentral()
    mavenLocal()
}

publishing {
    publications {
        create<MavenPublication>("library") {
            artifactId = project.name
            from(components["java"])
            versionMapping {
                usage("java-api") {
                    fromResolutionOf("runtimeClasspath")
                }
                usage("java-runtime") {
                    fromResolutionResult()
                }
            }
        }
    }
    repositories {
        mavenLocal()
        maven {
            name = "GitLab"
            url = uri("https://git.uzum.io/api/v4/projects/265/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                name = "Job-Token"
                value = System.getenv("CI_JOB_TOKEN")
            }
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }
}

val generatedSourcesDir = "$projectDir/src/main/java"

openApiGenerate {
    generatorName.set("spring")
    inputSpec.set("${project.rootDir}/user-notification-manager-impl/build/swagger/swagger.yaml")
    outputDir.set(generatedSourcesDir)

    apiPackage.set("uz.uzum.usernotificationmanager.api")
    modelPackage.set("uz.uzum.usernotificationmanager.model")

    version.set(project.version.toString())
    groupId.set(project.group.toString())

    globalProperties.set(
        mapOf(
            "skipOverwrite" to "false",
        ),
    )

    configOptions.set(
        mapOf(
            "dateLibrary" to "java8",
            "interfaceOnly" to "true",
            "useTags" to "true",
            "openApiNullable" to "true",
            "skipDefaultInterface" to "true",
            "useOptional" to "false",
            "sourceFolder" to "",
            "useSpringBoot3" to "true",
            "optionalAcceptNullable" to "false",
            "hideGenerationTimestamp" to "true",
            "removeEnumValuePrefix" to "true",
            "additionalModelTypeAnnotations" to
                """
                @lombok.Setter
                @lombok.Getter
                @lombok.Builder
                @lombok.NoArgsConstructor
                @lombok.AllArgsConstructor
                """.trimIndent(),
        ),
    )
    validateSpec.set(true)
}

tasks.named("openApiGenerate") {
    dependsOn(":user-notification-manager-impl:generateOpenApiDocs")
    outputs.upToDateWhen { false }
    finalizedBy("spotlessApply")
}

tasks.jar {
    enabled = true
    archiveClassifier.set("")
}
