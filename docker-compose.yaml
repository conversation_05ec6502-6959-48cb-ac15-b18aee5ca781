---
services:
  kafka:
    image: docker.io/bitnami/kafka:4.0
    ports:
      - "9092:9092"
    volumes:
      - "kafka_data:/bitnami"
    environment:
      # KRaft настройки
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      # Listeners
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
      # Здесь важно: вместо PLAINTEXT://:9092 задаем внешний адрес, например localhost:9092
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=PLAINTEXT
    networks:
      user-notification-manager: null

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - "8081:8081"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
    depends_on:
      - kafka
    networks:
      - user-notification-manager

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"    # AMQP-порт
      - "15672:15672"  # Веб-интерфейс управления
    networks:
      user-notification-manager: null

  prometheus:
    image: "prom/prometheus:latest"
    container_name: "user-notification-manager-prometheus"
    ports:
      - "9090:9090"
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
    volumes:
      - "./config/prometheus.yaml:/etc/prometheus/prometheus.yml:ro"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      user-notification-manager: null
  grafana:
    image: "grafana/grafana:latest"
    container_name: "user-notification-manager-grafana"
    depends_on:
      - "prometheus"
    volumes:
      - "./config/grafana/datasources:/etc/grafana/provisioning/datasources/"
      - "./config/grafana/dashboards:/etc/grafana/provisioning/dashboards/"
    ports:
      - "3000:3000"
    environment:
      - "GF_SECURITY_ADMIN_USER=admin"
      - "GF_SECURITY_ADMIN_PASSWORD=admin"
    networks:
      user-notification-manager: null
  postgres:
    image: "postgres:latest"
    container_name: "user-notification-manager-postgres"
    restart: "always"
    environment:
      - "POSTGRES_USER=postgres"
      - "POSTGRES_PASSWORD=postgres"
    volumes:
      - "pg-data:/var/lib/postgresql/data"
    ports:
      - "54322:5432"
    networks:
      user-notification-manager: null
  sentry:
    image: "wiremock/wiremock:latest"
    container_name: "user-notification-manager-sentry"
    ports:
      - "10100:8080"
    volumes:
      - "./infrastructure/external-services/user-notification-manager-sentry/:/home/<USER>/"
    command: "--disable-banner --disable-gzip"
volumes:
  pg-data:
    driver: "local"
  kafka_data:
    driver: local
networks:
  user-notification-manager: null
