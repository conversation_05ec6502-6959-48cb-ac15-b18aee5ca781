<project version="4">
  <component name="ProjectCodeStyleSettingsManager">
    <option name="PER_PROJECT_SETTINGS">
      <value>
        <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
        <option name="ALIGN_MULTILINE_ASSIGNMENT" value="true" />
        <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
        <option name="ALIGN_MULTILINE_EXTENDS_LIST" value="true" />
        <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="true" />
        <option name="ALIGN_MULTILINE_PARENTHESIZED_EXPRESSION" value="true" />
        <option name="ALIGN_MULTILINE_TERNARY_OPERATION" value="true" />
        <option name="ALIGN_MULTILINE_THROWS_LIST" value="true" />
        <option name="ARRAY_INITIALIZER_WRAP" value="1" />
        <option name="ASSIGNMENT_WRAP" value="5" />
        <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
        <option name="BINARY_OPERATION_WRAP" value="1" />
        <option name="BLANK_LINES_AFTER_CLASS_HEADER" value="1" />
        <option name="CALL_PARAMETERS_WRAP" value="1" />
        <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
        <option name="DOWHILE_BRACE_FORCE" value="3" />
        <option name="EXTENDS_KEYWORD_WRAP" value="1" />
        <option name="EXTENDS_LIST_WRAP" value="1" />
        <option name="FOR_BRACE_FORCE" value="3" />
        <option name="FOR_STATEMENT_WRAP" value="1" />
        <option name="IF_BRACE_FORCE" value="3" />
        <option name="IMPORT_LAYOUT_TABLE">
          <value>
            <package name="" static="true" withSubpackages="true" />
            <emptyLine />
            <package name="" static="false" withSubpackages="true" />
          </value>
        </option>
        <option name="INSERT_INNER_CLASS_IMPORTS" value="true" />
        <GroovyCodeStyleSettings>
          <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
          <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
          <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
            <value />
          </option>
          <option name="IMPORT_LAYOUT_TABLE">
            <value>
              <package name="" withSubpackages="true" static="true" />
              <emptyLine />
              <package name="" withSubpackages="true" static="false" />
            </value>
          </option>
          <!-- Should be a superset of https://github.com/google/error-prone/blob/c481b3f9c2da112db36ccfcbf64e755261a127ab/core/src/main/java/com/google/errorprone/bugpatterns/BadImport.java#L63 -->
          <DO_NOT_IMPORT_INNER>
            <CLASS name="Builder" />
            <CLASS name="BuilderFactory" />
            <CLASS name="Callback" />
            <CLASS name="Class" />
            <CLASS name="Entry" />
            <CLASS name="Enum" />
            <CLASS name="Factory" />
            <CLASS name="Type" />
            <CLASS name="Key" />
            <CLASS name="Id" />
            <CLASS name="Identifier" />
            <CLASS name="Provider" />
            <CLASS name="Visitor" />
          </DO_NOT_IMPORT_INNER>
        </GroovyCodeStyleSettings>
        <option name="JD_ALIGN_EXCEPTION_COMMENTS" value="false" />
        <option name="JD_ALIGN_PARAM_COMMENTS" value="false" />
        <option name="JD_DO_NOT_WRAP_ONE_LINE_COMMENTS" value="true" />
        <option name="JD_KEEP_EMPTY_EXCEPTION" value="false" />
        <option name="JD_KEEP_EMPTY_PARAMETER" value="false" />
        <option name="JD_KEEP_EMPTY_RETURN" value="false" />
        <option name="JD_PRESERVE_LINE_FEEDS" value="true" />
        <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
        <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
        <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
        <option name="METHOD_PARAMETERS_WRAP" value="1" />
        <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
        <option name="OPTIMIZE_IMPORTS_ON_THE_FLY" value="true" />
        <option name="OTHER_INDENT_OPTIONS">
          <value>
            <option name="INDENT_SIZE" value="4" />
            <option name="CONTINUATION_INDENT_SIZE" value="8" />
            <option name="TAB_SIZE" value="4" />
            <option name="USE_TAB_CHARACTER" value="false" />
            <option name="SMART_TABS" value="false" />
            <option name="LABEL_INDENT_SIZE" value="0" />
            <option name="LABEL_INDENT_ABSOLUTE" value="false" />
            <option name="USE_RELATIVE_INDENTS" value="false" />
          </value>
        </option>
        <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
          <value />
        </option>
        <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
        <option name="TERNARY_OPERATION_WRAP" value="1" />
        <option name="THROWS_KEYWORD_WRAP" value="1" />
        <option name="THROWS_LIST_WRAP" value="1" />
        <option name="WHILE_BRACE_FORCE" value="3" />
        <option name="WRAP_COMMENTS" value="true" />
        <codeStyleSettings language="JAVA">
          <indentOptions>
            <option name="CONTINUATION_INDENT_SIZE" value="8" />
          </indentOptions>
          <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
          <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
          <option name="ALIGN_MULTILINE_RESOURCES" value="false" />
          <option name="ARRAY_INITIALIZER_WRAP" value="1" />
          <option name="ASSIGNMENT_WRAP" value="1" />
          <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
          <option name="BINARY_OPERATION_WRAP" value="1" />
          <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="0" />
          <option name="CALL_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
          <option name="CALL_PARAMETERS_WRAP" value="5" />
          <option name="DOWHILE_BRACE_FORCE" value="3" />
          <option name="ENUM_CONSTANTS_WRAP" value="2" />
          <option name="EXTENDS_KEYWORD_WRAP" value="1" />
          <option name="EXTENDS_LIST_WRAP" value="1" />
          <option name="FIELD_ANNOTATION_WRAP" value="1" />
          <option name="FOR_BRACE_FORCE" value="3" />
          <option name="FOR_STATEMENT_WRAP" value="1" />
          <option name="IF_BRACE_FORCE" value="3" />
          <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
          <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
          <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
          <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
          <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
          <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="true" />
          <option name="KEEP_SIMPLE_LAMBDAS_IN_ONE_LINE" value="true" />
          <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
          <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
          <option name="LINE_COMMENT_ADD_SPACE" value="true" />
          <option name="METHOD_CALL_CHAIN_WRAP" value="5" />
          <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
          <option name="METHOD_PARAMETERS_WRAP" value="5" />
          <option name="OPTIMIZE_IMPORTS_ON_THE_FLY" value="true" />
          <option name="PARENT_SETTINGS_INSTALLED" value="true" />
          <option name="RESOURCE_LIST_WRAP" value="5" />
          <option name="RIGHT_MARGIN" value="120" />
          <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
          <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
          <option name="TERNARY_OPERATION_WRAP" value="5" />
          <option name="THROWS_KEYWORD_WRAP" value="1" />
          <option name="THROWS_LIST_WRAP" value="1" />
          <option name="WHILE_BRACE_FORCE" value="3" />
          <option name="WRAP_ON_TYPING" value="1" />
          <arrangement>
            <rules>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PUBLIC>true</PUBLIC>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PROTECTED>true</PROTECTED>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PRIVATE>true</PRIVATE>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PUBLIC>true</PUBLIC>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PROTECTED>true</PROTECTED>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PRIVATE>true</PRIVATE>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <INITIALIZER_BLOCK>true</INITIALIZER_BLOCK>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PUBLIC>true</PUBLIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PROTECTED>true</PROTECTED>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <FINAL>true</FINAL>
                      <PRIVATE>true</PRIVATE>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PUBLIC>true</PUBLIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PROTECTED>true</PROTECTED>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <FIELD>true</FIELD>
                      <PRIVATE>true</PRIVATE>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <FIELD>true</FIELD>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <INITIALIZER_BLOCK>true</INITIALIZER_BLOCK>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <CONSTRUCTOR>true</CONSTRUCTOR>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <METHOD>true</METHOD>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <METHOD>true</METHOD>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <ENUM>true</ENUM>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <INTERFACE>true</INTERFACE>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <CLASS>true</CLASS>
                      <STATIC>true</STATIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <CLASS>true</CLASS>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <ABSTRACT>true</ABSTRACT>
                      <METHOD>true</METHOD>
                      <PUBLIC>true</PUBLIC>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <ABSTRACT>true</ABSTRACT>
                      <METHOD>true</METHOD>
                      <PROTECTED>true</PROTECTED>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <METHOD>true</METHOD>
                      <PUBLIC>true</PUBLIC>
                      <SYNCHRONIZED>true</SYNCHRONIZED>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <METHOD>true</METHOD>
                      <PROTECTED>true</PROTECTED>
                      <SYNCHRONIZED>true</SYNCHRONIZED>
                    </AND>
                  </match>
                </rule>
              </section>
              <section>
                <rule>
                  <match>
                    <AND>
                      <METHOD>true</METHOD>
                      <PRIVATE>true</PRIVATE>
                      <SYNCHRONIZED>true</SYNCHRONIZED>
                    </AND>
                  </match>
                </rule>
              </section>
            </rules>
          </arrangement>
        </codeStyleSettings>
        <codeStyleSettings language="Groovy">
          <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
          <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
          <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
          <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="0" />
          <option name="SPACE_WITHIN_BRACES" value="false" />
          <option name="CALL_PARAMETERS_WRAP" value="1" />
          <option name="METHOD_PARAMETERS_WRAP" value="1" />
          <option name="EXTENDS_LIST_WRAP" value="1" />
          <option name="THROWS_LIST_WRAP" value="1" />
          <option name="EXTENDS_KEYWORD_WRAP" value="1" />
          <option name="THROWS_KEYWORD_WRAP" value="1" />
          <option name="BINARY_OPERATION_WRAP" value="1" />
          <option name="TERNARY_OPERATION_WRAP" value="1" />
          <option name="ASSIGNMENT_WRAP" value="1" />
          <option name="FOR_BRACE_FORCE" value="3" />
        </codeStyleSettings>
      </value>
    </option>
    <option name="USE_PER_PROJECT_SETTINGS" value="true" />
  </component>
</project>
