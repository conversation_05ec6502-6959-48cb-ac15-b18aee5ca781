pipeline:
  build:
    - graddle
  docker:
    - build-image
  test:
    - graddle-spotless
  #    - graddle-test
  swagger:
    - build-swagger
    - validate-swagger
    - trigger-swagger
  deploy:
    - deploy-preprod


variables:
  SERVER_PORT: 8001
  SPRING_PROFILES_ACTIVE: build-test
  DB_NAME: postgres
  SPRING_DB_URL: ****************************************
  SPRING_DB_USERNAME: postgres
  SPRING_DB_PASSWORD: postgres
  POSTGRES_DB: postgres
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  LIQUIBASE_DEFAULT_SCHEMA: public
  KAFKA_BOOTSTRAP_SERVERS: kafka-broker:9092
  MQ_LOG_PASS: guest
  RABBITMQ_HOST: localhost
  RABBITMQ_PORT: 5672
  RABBITMQ_USERNAME: $MQ_LOG_PASS
  RABBITMQ_PASSWORD: $MQ_LOG_PASS
  RABBITMQ_QUEUE: queue.apelsin.direct
  RABBITMQ_EXCHANGE: fcm.topic
  RABBITMQ_ROUTING_KEY: apelsin.direct.#
  KAFKA_NOTIFICATIONS_UPDATE_TOPIC: notifications.document.update

services:
  - postgres
  - kafka-broker
  - rabbitmq

jobs:
  graddle:
    image: 8.6.0-jdk21-alpine
    script:
      - gradle clean bootjar
    artifacts:
      - user-notification-manager-impl/build/classes/java/main
      - user-notification-manager-impl/build/resources/main
      - user-notification-manager-impl/build/libs/*.jar
  graddle-test:
    image: 8.6.0-jdk21-alpine
  build-swagger:
    type: graddle
    image: 8.6.0-jdk21-alpine
    cmd: gradle
    options: clean generateOpenApiDocs
    settings: -Dspring.profiles.active=build-swagger -Dspringdoc.api-docs.enabled=true
    swagger_file: build/swagger/user-notification-manager-openapi.yaml
  deploy-preprod:
    service: user-notification-manager
