---
spring:
  profiles:
    active: "testcontainers"
  application:
    name: "user-notification-manager"
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:19094,localhost:19095,localhost:19096}
    consumer:
      group-id: user-notification-manager
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        allow.auto.create.topics: true
        spring.json.trusted.packages: "*"
        spring.json.type.mapping: "KycDocumentUpdateEvent:uz.uzum.usernotificationmanager.dto.kafka.IdentityDocumentIncomingRequestDto"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
  liquibase:
    change-log: "classpath:db/changelog/db.changelog-master.yml"
    enabled: false
  jackson:
    time-zone: "UTC"
