package uz.uzum.usernotificationmanager.repository;

import java.util.Optional;
import java.util.UUID;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.persistent.PushNotificationSettingsEntity;

@Repository
public interface PushNotificationSettingsRepository extends CrudRepository<PushNotificationSettingsEntity, UUID> {
    Optional<PushNotificationSettingsEntity> findByEventType(EventType eventType);
}
