package uz.uzum.usernotificationmanager.repository;

import java.util.Optional;
import java.util.UUID;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.persistent.EventTypeToNotificationTypeEntity;

@Repository
public interface EventTypeToNotificationTypeRepository extends CrudRepository<EventTypeToNotificationTypeEntity, UUID> {
    Optional<EventTypeToNotificationTypeEntity> findByEventType(EventType eventType);
}
