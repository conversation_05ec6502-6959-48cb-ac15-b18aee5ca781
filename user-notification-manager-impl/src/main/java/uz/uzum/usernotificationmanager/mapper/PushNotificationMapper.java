package uz.uzum.usernotificationmanager.mapper;

import jakarta.validation.constraints.NotNull;
import java.util.Map;
import uz.uzum.usernotificationmanager.dto.rabbitmq.PushNotificationOutgoingRequestDto;
import uz.uzum.usernotificationmanager.model.PushNotification;

public class PushNotificationMapper {
    public static @NotNull PushNotificationOutgoingRequestDto toDto(@NotNull PushNotification pushNotification) {
        String fcmToken = pushNotification.fcmToken();
        Boolean isSilent = pushNotification.isSilent();
        String title = pushNotification.title();
        String body = pushNotification.body();
        Map<String, String> data = pushNotification.data();
        String imageUrl = pushNotification.imageUrl();

        return new PushNotificationOutgoingRequestDto(fcmToken, isSilent, title, body, data, imageUrl);
    }
}
