package uz.uzum.usernotificationmanager.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import uz.uzum.usernotificationmanager.dto.NotificationDto;
import uz.uzum.usernotificationmanager.model.PushNotification;
import uz.uzum.usernotificationmanager.model.button.Button;
import uz.uzum.usernotificationmanager.model.enums.Lang;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;

@Mapper(componentModel = "spring")
public interface NotificationDtoMapper {

    @Mapping(target = "userId", source = "pushNotification.userId")
    @Mapping(target = "title", source = "pushNotification.title")
    @Mapping(target = "body", source = "pushNotification.body")
    @Mapping(target = "imageUrl", source = "pushNotification.imageUrl")
    @Mapping(target = "lang", source = "lang")
    @Mapping(target = "button", ignore = true)
    @Mapping(target = "notificationType", source = "notificationType")
    NotificationDto toDto(PushNotification pushNotification, Lang lang, NotificationType notificationType);

    @Mapping(target = "text", source = "button.title")
    @Mapping(target = "actionType", source = "button.action.type")
    @Mapping(target = "actionUrl", source = "button.action.payload")
    NotificationDto.ButtonDto toButtonDto(Button button);
}
