package uz.uzum.usernotificationmanager.mapper;

import jakarta.validation.constraints.NotNull;
import uz.uzum.usernotificationmanager.model.PushNotification;
import uz.uzum.usernotificationmanager.model.enums.PushNotificationStatus;
import uz.uzum.usernotificationmanager.model.persistent.PushNotificationEntity;

public class PushNotificationEntityMapper {

    public static @NotNull PushNotificationEntity toEntity(
            @NotNull PushNotification pushNotification, PushNotificationStatus status) {
        return new PushNotificationEntity(
                pushNotification.uuid(),
                pushNotification.fcmToken(),
                status,
                pushNotification.eventType(),
                pushNotification.isSilent(),
                pushNotification.title(),
                pushNotification.body(),
                pushNotification.data(),
                pushNotification.imageUrl());
    }
}
