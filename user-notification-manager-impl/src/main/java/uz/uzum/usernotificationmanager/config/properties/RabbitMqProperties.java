package uz.uzum.usernotificationmanager.config.properties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Getter
@Validated
@AllArgsConstructor
@ConfigurationProperties(prefix = "rabbitmq")
public class RabbitMqProperties {

    private String queue;
    private String exchange;
    private String routingKey;
}
