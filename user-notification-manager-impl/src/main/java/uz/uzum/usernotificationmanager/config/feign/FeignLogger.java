package uz.uzum.usernotificationmanager.config.feign;

import feign.Logger;
import feign.Request;
import feign.Response;
import java.io.IOException;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import uz.uzum.logformatter.extractor.ContentExtractor;
import uz.uzum.logformatter.util.HttpUtils;
import uz.uzum.logformatter.util.LogAttributesUtils;

@Slf4j
@RequiredArgsConstructor
public class FeignLogger extends Logger {

    private final ContentExtractor contentExtractor;

    @Override
    protected void log(String configKey, String format, Object... args) {}

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime)
            throws IOException {
        long endTime = System.currentTimeMillis();
        response = logFeignRequestResponse(response, endTime - elapsedTime, endTime);
        return response;
    }

    private Response logFeignRequestResponse(Response response, Long startTime, Long endTime) {
        try {
            Map<String, Object> attributes = LogAttributesUtils.extract(response);
            Request request = response.request();
            Request.HttpMethod method = request.httpMethod();
            String url = request.url();
            String requestContentType =
                    HttpUtils.getContentType(request.headers()).orElse(MediaType.APPLICATION_JSON_VALUE);
            LogAttributesUtils.addAttributes(
                            log.atInfo(),
                            attributes,
                            request.body(),
                            request.charset(),
                            requestContentType,
                            contentExtractor::extractContent)
                    .log("Outgoing feign request {}, {}", method, url);

            // Log Feign Response
            // Check if the response has a body
            int status = response.status();
            if (response.body() != null && !(status == 204 || status == 205)) {
                // HTTP 204 No Content "...response MUST NOT include a message-body"
                // HTTP 205 Reset Content "...response MUST NOT include an entity"
                byte[] bodyData = response.body().asInputStream().readAllBytes();
                attributes.put("response.status", String.valueOf(response.status()));
                String responseContentType =
                        HttpUtils.getContentType(response.headers()).orElse(MediaType.APPLICATION_JSON_VALUE);

                LogAttributesUtils.addAttributes(
                                log.atInfo(),
                                attributes,
                                bodyData,
                                response.charset(),
                                responseContentType,
                                contentExtractor::extractContent)
                        .log(
                                "Incoming feign response {}, {}",
                                response.request().httpMethod(),
                                response.request().url());

                return response.toBuilder().body(bodyData).build();
            }
            return response;
        } catch (Exception e) {
            LogAttributesUtils.addThrowable(e).log(e.getMessage());
            return response;
        }
    }
}
