package uz.uzum.usernotificationmanager.config.web;

import static uz.uzum.logformatter.util.LogAttributesUtils.addAttributes;
import static uz.uzum.logformatter.util.LogAttributesUtils.extract;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import uz.uzum.logformatter.extractor.ContentExtractor;

@Component
@Slf4j
@RequiredArgsConstructor
public class LogFilter extends BaseFilter {

    private final ContentExtractor contentExtractor;

    private static final String RESPONSE_STATUS_ATTRIBUTE_NAME = "response.status";

    @Override
    protected void doFilterInternal(
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain)
            throws ServletException, IOException {
        if (isAsyncDispatch(httpServletRequest)) {
            filterChain.doFilter(httpServletRequest, httpServletResponse);
        } else {
            doFilterWrapped(wrapRequest(httpServletRequest), wrapResponse(httpServletResponse), filterChain);
        }
    }

    private static ContentCachingRequestWrapper wrapRequest(HttpServletRequest request) {
        if (request instanceof ContentCachingRequestWrapper requestWrapper) {
            return requestWrapper;
        } else {
            return new ContentCachingRequestWrapper(request);
        }
    }

    private static ContentCachingResponseWrapper wrapResponse(HttpServletResponse response) {
        if (response instanceof ContentCachingResponseWrapper responseWrapper) {
            return responseWrapper;
        } else {
            return new ContentCachingResponseWrapper(response);
        }
    }

    private void doFilterWrapped(
            ContentCachingRequestWrapper request, ContentCachingResponseWrapper response, FilterChain filterChain)
            throws IOException, ServletException {
        try {
            filterChain.doFilter(request, response);
        } finally {
            Map<String, Object> attributes = extract(request);
            JsonNode requestContent = contentExtractor.extractContent(
                    request.getContentAsByteArray(), request.getCharacterEncoding(), request.getContentType());
            addAttributes(log.atInfo(), attributes, requestContent)
                    .log("Incoming Request {}, {}", request.getMethod(), request.getRequestURI());

            attributes.put(RESPONSE_STATUS_ATTRIBUTE_NAME, response.getStatus());
            JsonNode responseContent = contentExtractor.extractContent(
                    response.getContentAsByteArray(), response.getCharacterEncoding(), response.getContentType());
            addAttributes(log.atInfo(), attributes, responseContent)
                    .log("Outgoing Response {}, {}", request.getMethod(), request.getRequestURI());
            response.copyBodyToResponse();
        }
    }
}
