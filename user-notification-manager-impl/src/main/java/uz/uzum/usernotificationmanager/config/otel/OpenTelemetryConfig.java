package uz.uzum.usernotificationmanager.config.otel;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.sdk.logs.SdkLoggerProvider;
import io.opentelemetry.sdk.trace.SdkTracerProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import uz.uzum.logformatter.config.OpenTelemetrySdkComponentsFactory;
import uz.uzum.logformatter.extractor.ContentExtractor;
import uz.uzum.logformatter.format.OtelLogFormatter;

@Configuration
@RequiredArgsConstructor
public class OpenTelemetryConfig {

    @Value("${spring.application.name}")
    private final String serviceName;

    @Bean
    public OpenTelemetry openTelemetry() {
        SdkTracerProvider tracerProvider = OpenTelemetrySdkComponentsFactory.createTracerProvider();
        SdkLoggerProvider loggerProvider =
                OpenTelemetrySdkComponentsFactory.createLoggerProvider(new OtelLogFormatter(), serviceName);
        return OpenTelemetrySdkComponentsFactory.createOpenTelemetryForLogback(loggerProvider, tracerProvider);
    }

    @Bean
    public Tracer tracer(OpenTelemetry openTelemetry) {
        return openTelemetry.getTracer("%s-tracer".formatted(serviceName));
    }

    @Bean
    public ContentExtractor contentExtractor() {
        return new ContentExtractor();
    }
}
