package uz.uzum.usernotificationmanager.config.feign;

import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import uz.uzum.usernotificationmanager.config.properties.ApelsinProperties;

@Configuration
public class ApelsinFeignConfig {

    private final ApelsinProperties apelsinProperties;

    public ApelsinFeignConfig(ApelsinProperties apelsinProperties) {
        this.apelsinProperties = apelsinProperties;
    }

    @Bean
    public RequestInterceptor apelsinApiKeyInterceptor() {
        return requestTemplate ->
                requestTemplate.header("X-API-Key", apelsinProperties.api().key());
    }
}
