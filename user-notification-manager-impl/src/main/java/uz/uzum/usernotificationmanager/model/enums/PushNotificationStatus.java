/*
 * (c) Copyright 2025 Palantir Technologies Inc. All rights reserved.
 */

package uz.uzum.usernotificationmanager.model.enums;

/**
 * Enum representing the status of a push notification.
 */
public enum PushNotificationStatus {
    /**
     * The notification has been sent to the FCM service.
     */
    SENT,

    /**
     * The notification has been delivered to the device.
     */
    DELIVERED,

    /**
     * The notification failed to be sent or delivered.
     */
    FAILED
}
