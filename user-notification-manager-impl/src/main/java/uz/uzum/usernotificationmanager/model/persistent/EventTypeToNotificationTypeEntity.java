package uz.uzum.usernotificationmanager.model.persistent;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import java.util.UUID;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;

@Table(name = "event_type_to_notification_type")
public record EventTypeToNotificationTypeEntity(
        @Id UUID uuid,
        @Enumerated(EnumType.STRING) EventType eventType,
        @Enumerated(EnumType.STRING) NotificationType notificationType) {}
