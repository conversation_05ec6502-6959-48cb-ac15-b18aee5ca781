package uz.uzum.usernotificationmanager.producer.rabbitmq;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import uz.uzum.usernotificationmanager.config.properties.RabbitMqProperties;
import uz.uzum.usernotificationmanager.dto.rabbitmq.PushNotificationOutgoingRequestDto;

@Slf4j
@RequiredArgsConstructor
@Component
public class FcmProducer {

    private final RabbitTemplate rabbitTemplate;
    private final RabbitMqProperties rabbitMqProperties;

    public void send(@NotNull PushNotificationOutgoingRequestDto message) {
        log.info("Sending FCM Notification: {}", message);

        rabbitTemplate.convertAndSend(rabbitMqProperties.getExchange(), rabbitMqProperties.getRoutingKey(), message);
    }
}
