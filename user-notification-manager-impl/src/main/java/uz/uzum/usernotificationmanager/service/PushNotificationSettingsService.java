package uz.uzum.usernotificationmanager.service;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.persistent.PushNotificationSettingsEntity;
import uz.uzum.usernotificationmanager.repository.PushNotificationSettingsRepository;

@Service
@AllArgsConstructor
public class PushNotificationSettingsService {
    private final PushNotificationSettingsRepository pushNotificationSettingsRepository;

    public @NotNull PushNotificationSettingsEntity findByEventType(EventType eventType) {
        return pushNotificationSettingsRepository
                .findByEventType(eventType)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_IMPLEMENTED,
                        String.format(
                                "PushNotificationSettingsEntity with eventType '%s' not found", eventType.toString())));
    }
}
