package uz.uzum.usernotificationmanager.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import uz.uzum.usernotificationmanager.client.ApelsinClient;
import uz.uzum.usernotificationmanager.dto.kafka.IdentityDocumentIncomingRequestDto;
import uz.uzum.usernotificationmanager.mapper.NotificationDtoMapper;
import uz.uzum.usernotificationmanager.mapper.PushNotificationEntityMapper;
import uz.uzum.usernotificationmanager.mapper.PushNotificationMapper;
import uz.uzum.usernotificationmanager.model.PushNotification;
import uz.uzum.usernotificationmanager.model.button.ButtonActionType;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;
import uz.uzum.usernotificationmanager.model.enums.PushNotificationStatus;
import uz.uzum.usernotificationmanager.model.persistent.PushNotificationEntity;
import uz.uzum.usernotificationmanager.model.persistent.PushNotificationSettingsEntity;
import uz.uzum.usernotificationmanager.producer.rabbitmq.FcmProducer;
import uz.uzum.usernotificationmanager.repository.PushNotificationRepository;

@Service
@AllArgsConstructor
public class PushNotificationService {
    private final FcmProducer fcmProducer;
    private final PushNotificationRepository pushNotificationRepository;
    private final ApelsinClient apelsinClient;
    private final NotificationDtoMapper notificationDtoMapper;
    private final ObjectMapper objectMapper;

    public @NotNull PushNotification createPushNotification(
            NotificationType notificationType,
            IdentityDocumentIncomingRequestDto message,
            PushNotificationSettingsEntity settings,
            String fcmToken) {
        UUID uuid = UUID.randomUUID();
        EventType eventType = settings.eventType();
        String userId = message.userId();
        Boolean isSilent = settings.isSilent();
        String title = settings.titleTemplate() + " " + message.title();
        String body = settings.bodyTemplate() + " " + message.subTitle();
        Map<String, String> data = new HashMap<>(settings.data());
        data.put("notificationType", notificationType.name());
        if (Objects.nonNull(message.button())) {
            try {
                data.put("button", objectMapper.writeValueAsString(message.button()));
            } catch (JsonProcessingException ex) {
                Sentry.captureException(ex);
            }
            String payload = message.button().action().payload();
            ButtonActionType buttonActionType = message.button().action().type();
            if (buttonActionType.equals(ButtonActionType.DEEPLINK)) {
                data.put("deep_link", payload);
            } else if (buttonActionType.equals(ButtonActionType.ACTION)) {
                data.put("actionUrl", payload);
            }
        }
        String imageUrl = settings.imageUrl();

        return new PushNotification(uuid, eventType, userId, fcmToken, isSilent, title, body, data, imageUrl);
    }

    public void sendNotifications(List<? extends PushNotification> notifications) {
        List<PushNotificationEntity> entitiesToSave = notifications.stream()
                .map(notification -> PushNotificationEntityMapper.toEntity(notification, PushNotificationStatus.SENT))
                .toList();
        pushNotificationRepository.saveAll(entitiesToSave);
        notifications.stream().map(PushNotificationMapper::toDto).forEach(fcmProducer::send);
    }

    public void sendNotificationToApelsin(
            PushNotification notification,
            IdentityDocumentIncomingRequestDto message,
            NotificationType notificationType) {
        apelsinClient.saveNotification(notificationDtoMapper.toDto(notification, message.lang(), notificationType));
    }
}
