package uz.uzum.usernotificationmanager.service;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;
import uz.uzum.usernotificationmanager.model.persistent.EventTypeToNotificationTypeEntity;
import uz.uzum.usernotificationmanager.repository.EventTypeToNotificationTypeRepository;

@Service
@AllArgsConstructor
public class EventTypeToNotificationTypeService {
    private final EventTypeToNotificationTypeRepository eventTypeToNotificationTypeRepository;

    public @NotNull NotificationType findNotificationTypeByEventType(@NotNull EventType eventType) {
        return eventTypeToNotificationTypeRepository
                .findByEventType(eventType)
                .map(EventTypeToNotificationTypeEntity::notificationType)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_IMPLEMENTED,
                        String.format("EventTypeToNotificationTypeEntity with eventType %s not found", eventType)));
    }
}
