package uz.uzum.usernotificationmanager.service;

import static uz.uzum.usernotificationmanager.model.enums.EventType.IDENTITY_DOCUMENT_UPDATE_FAILED;
import static uz.uzum.usernotificationmanager.model.enums.EventType.IDENTITY_DOCUMENT_UPDATE_FINISHED;
import static uz.uzum.usernotificationmanager.model.enums.EventType.IDENTITY_DOCUMENT_UPDATE_STARTED;
import static uz.uzum.usernotificationmanager.model.enums.NotificationType.PUSH;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import uz.uzum.usernotificationmanager.dto.http.UserDeviceDto;
import uz.uzum.usernotificationmanager.dto.kafka.IdentityDocumentIncomingRequestDto;
import uz.uzum.usernotificationmanager.model.PushNotification;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;
import uz.uzum.usernotificationmanager.model.persistent.PushNotificationSettingsEntity;

@Service
@RequiredArgsConstructor
@Slf4j
public class IdentityDocumentService {
    private final EventTypeToNotificationTypeService eventTypeToNotificationTypeService;
    private final PushNotificationSettingsService pushNotificationSettingsService;
    private final PushNotificationService pushNotificationService;
    private final UserDeviceService userDeviceService;

    public void processIdentityDocumentEvent(@NotNull IdentityDocumentIncomingRequestDto message) {
        try {
            EventType eventType;
            try {
                eventType = EventType.valueOf(message.eventType().toUpperCase());
            } catch (Exception ex) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unknown eventType: " + message.eventType());
            }

            NotificationType notificationType =
                    eventTypeToNotificationTypeService.findNotificationTypeByEventType(eventType);

            switch (eventType) {
                case IDENTITY_DOCUMENT_UPDATE_STARTED:
                    processDocumentUpdateStarted(notificationType, message);
                    break;
                case IDENTITY_DOCUMENT_UPDATE_FINISHED:
                    processDocumentUpdateFinished(notificationType, message);
                    break;
                case IDENTITY_DOCUMENT_UPDATE_FAILED:
                    processDocumentUpdateFailed(notificationType, message);
                    break;
                default:
                    throw new RuntimeException("Unexpected event type: " + message.eventType());
            }
        } catch (IllegalArgumentException e) {
            log.error("Unexpected event type: {}", message.eventType());
            throw new RuntimeException("Invalid event type: " + message.eventType(), e);
        }
    }

    private void processDocumentUpdateStarted(
            @NotNull NotificationType notificationType, @NotNull IdentityDocumentIncomingRequestDto message) {
        if (!notificationType.equals(PUSH)) {
            throw new ResponseStatusException(
                    HttpStatus.NOT_IMPLEMENTED,
                    String.format(
                            "Unsupported notification type '%s' for eventType '%s'",
                            notificationType, IDENTITY_DOCUMENT_UPDATE_STARTED));
        }

        PushNotificationSettingsEntity settings =
                pushNotificationSettingsService.findByEventType(IDENTITY_DOCUMENT_UPDATE_STARTED);

        List<UserDeviceDto> userDevices = userDeviceService.getUserDevices(message.userId());

        List<String> fcmTokens =
                userDevices.stream().map(UserDeviceDto::fcmToken).toList();

        List<PushNotification> notifications = fcmTokens.stream()
                .map(fcmToken ->
                        pushNotificationService.createPushNotification(notificationType, message, settings, fcmToken))
                .toList();

        pushNotificationService.sendNotifications(notifications);

        if (notifications.isEmpty()) {
            return;
        }

        pushNotificationService.sendNotificationToApelsin(notifications.getFirst(), message, notificationType);
    }

    private void processDocumentUpdateFinished(
            @NotNull NotificationType notificationType, @NotNull IdentityDocumentIncomingRequestDto message) {
        if (!notificationType.equals(PUSH)) {
            throw new ResponseStatusException(
                    HttpStatus.NOT_IMPLEMENTED,
                    String.format(
                            "Unsupported notification type '%s' for eventType '%s'",
                            notificationType, IDENTITY_DOCUMENT_UPDATE_FINISHED));
        }
    }

    private void processDocumentUpdateFailed(
            @NotNull NotificationType notificationType, @NotNull IdentityDocumentIncomingRequestDto message) {
        if (!notificationType.equals(PUSH)) {
            throw new ResponseStatusException(
                    HttpStatus.NOT_IMPLEMENTED,
                    String.format(
                            "Unsupported notification type '%s' for eventType '%s'",
                            notificationType, IDENTITY_DOCUMENT_UPDATE_FAILED));
        }
    }
}
