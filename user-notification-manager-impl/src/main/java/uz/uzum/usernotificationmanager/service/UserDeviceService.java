/*
 * (c) Copyright 2025 Palantir Technologies Inc. All rights reserved.
 */

package uz.uzum.usernotificationmanager.service;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import uz.uzum.usernotificationmanager.client.ApelsinClient;
import uz.uzum.usernotificationmanager.dto.http.UserDeviceDto;

/**
 * Service for managing user devices.
 */
@Service
@AllArgsConstructor
public class UserDeviceService {
    private final ApelsinClient apelsinClient;

    /**
     * Fetches user devices for a specific user from the Apelsin service.
     *
     * @param userId the ID of the user whose devices to fetch
     * @return a list of user devices
     */
    public @NotNull List<UserDeviceDto> getUserDevices(String userId) {
        return apelsinClient.getUserDevices(userId);
    }
}
