package uz.uzum.usernotificationmanager.consumer.kafka;

import io.sentry.Sentry;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import uz.uzum.usernotificationmanager.dto.kafka.IdentityDocumentIncomingRequestDto;
import uz.uzum.usernotificationmanager.service.IdentityDocumentService;

@Slf4j
@Component
@RequiredArgsConstructor
public class IdentityDocumentConsumer {

    private final IdentityDocumentService identityDocumentService;

    @KafkaListener(topics = "${kafka.topics.notifications-update}", groupId = "${spring.kafka.consumer.group-id}")
    public void handleDocumentUpdate(@NotNull IdentityDocumentIncomingRequestDto message) {
        try {
            identityDocumentService.processIdentityDocumentEvent(message);
            log.info("Received document update event: {}", message);
        } catch (Exception e) {
            Sentry.captureException(e);
            log.error("Error processing document update event", e);
        }
    }
}
