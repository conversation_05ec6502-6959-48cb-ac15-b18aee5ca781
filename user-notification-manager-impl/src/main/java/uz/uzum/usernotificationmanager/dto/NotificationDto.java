package uz.uzum.usernotificationmanager.dto;

import lombok.Builder;
import uz.uzum.usernotificationmanager.model.enums.Lang;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;

@Builder
public record NotificationDto(
        Long userId,
        String title,
        String body,
        String imageUrl,
        Lang lang,
        ButtonDto button,
        NotificationType notificationType) {

    @Builder
    public record ButtonDto(String text, String actionType, String actionUrl) {}
}
