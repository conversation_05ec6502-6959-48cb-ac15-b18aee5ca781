package uz.uzum.usernotificationmanager.client;

import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import uz.uzum.usernotificationmanager.client.fallback.ApelsinClientFallback;
import uz.uzum.usernotificationmanager.config.feign.ApelsinFeignConfig;
import uz.uzum.usernotificationmanager.dto.NotificationDto;
import uz.uzum.usernotificationmanager.dto.http.UserDeviceDto;

/**
 * Client for interacting with the Apelsin service to fetch user devices.
 * Uses Feign client with configuration that adds X-API-Key header to all requests.
 */
@FeignClient(
        name = "apelsin",
        url = "${apelsin.api.url}",
        configuration = ApelsinFeignConfig.class,
        fallback = ApelsinClientFallback.class)
public interface ApelsinClient {

    /**
     * Fetches user devices for a specific user from the Apelsin service.
     *
     * @param userId the ID of the user whose devices to fetch
     * @return a list of user devices
     */
    @GetMapping("/users/{userId}/devices")
    List<UserDeviceDto> getUserDevices(@PathVariable("userId") String userId);

    @PostMapping("/api/notification/save")
    void saveNotification(@RequestBody NotificationDto dto);
}
