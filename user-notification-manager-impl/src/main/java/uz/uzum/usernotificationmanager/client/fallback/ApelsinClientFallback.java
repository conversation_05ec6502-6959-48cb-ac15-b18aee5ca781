package uz.uzum.usernotificationmanager.client.fallback;

import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import uz.uzum.usernotificationmanager.client.ApelsinClient;
import uz.uzum.usernotificationmanager.dto.NotificationDto;
import uz.uzum.usernotificationmanager.dto.http.UserDeviceDto;

/**
 * Fallback implementation for the ApelsinClient.
 * Returns empty results and logs errors when the Apelsin service is unavailable.
 */
@Component
@Slf4j
public class ApelsinClientFallback implements ApelsinClient {

    @Override
    public List<UserDeviceDto> getUserDevices(String userId) {
        log.error("Error fetching user devices for user {}: Fallback method called", userId);
        return Collections.emptyList();
    }

    @Override
    public void saveNotification(NotificationDto dto) {
        log.error("Error saving notification: Fallback method called");
    }
}
