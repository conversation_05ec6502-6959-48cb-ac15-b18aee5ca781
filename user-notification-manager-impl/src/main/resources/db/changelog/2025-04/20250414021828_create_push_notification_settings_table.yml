databaseChangeLog:
  - changeSet:
      id: 20250414021435_create_event_type_to_notification_type_table
      author: <PERSON>

      preConditions:
        - onFail: MARK_RAN

      changes:
        - createTable:
            tableName: push_notification_settings
            columns:
              - column:
                  name: uuid
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: event_type
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: is_silent
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: title_template
                  type: text
              - column:
                  name: body_template
                  type: text
              - column:
                  name: data
                  type: jsonb
              - column:
                  name: image_url
                  type: text