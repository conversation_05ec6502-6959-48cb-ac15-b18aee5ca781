databaseChangeLog:
  - changeSet:
      id: 20250414022030_create_push_notification_table
      author: <PERSON>

      preConditions:
        - onFail: MARK_RAN

      changes:
        - createTable:
            tableName: push_notification
            columns:
              - column:
                  name: uuid
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: fcm_token
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: event_type
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: is_silent
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: title
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: body
                  type: text
              - column:
                  name: data
                  type: jsonb
              - column:
                  name: image_url
                  type: text