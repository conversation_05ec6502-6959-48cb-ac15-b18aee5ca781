databaseChangeLog:
  - changeSet:
      id: 20250414021435_create_event_type_to_notification_type_table
      author: <PERSON>

      preConditions:
        - onFail: MARK_RAN

      changes:
        - createTable:
            tableName: event_type_to_notification_type
            columns:
              - column:
                  name: uuid
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: event_type
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: notification_type
                  type: text
                  constraints:
                    nullable: false