databaseChangeLog:
    - changeSet:
          id: 20250414032357_add_identity_document_push_notification_type
          author: <PERSON>

          preConditions:
              - onFail: MARK_RAN

          changes:
              - insert:
                    tableName: event_type_to_notification_type
                    columns:
                        - column:
                              name: uuid
                              value: "9fbd0df5-8b7f-4a14-bf24-1b8f5c60d9f7"
                        - column:
                              name: event_type
                              value: IDENTITY_DOCUMENT_UPDATE_STARTED
                        - column:
                              name: notification_type
                              value: PUSH