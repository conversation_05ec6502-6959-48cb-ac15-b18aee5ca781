---
spring:
  application:
    name: "user-notification-manager"
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
  kafka:
    bootstrap-servers: localhost:19094,localhost:29094,localhost:39094
    consumer:
      group-id: user-notification-manager
      properties:
        allow.auto.create.topics: true
        spring.json.trusted.packages: "*"
        spring.json.type.mapping: "com.userbff.model.notification.NotificationEvent:uz.uzum.usernotificationmanager.dto.kafka.IdentityDocumentIncomingRequestDto"
  datasource:
    url: "******************************************"
    username: "postgres"
    password: "postgres"
    driver-class-name: "org.postgresql.Driver"
  liquibase:
    change-log: "classpath:db/changelog/db.changelog-master.yml"
  jackson:
    time-zone: "UTC"
rabbitmq:
  queue: queue.apelsin.direct
  exchange: fcm.topic
  routing-key: apelsin.direct.#
kafka:
  topics:
    notifications-update: notifications.document.update
apelsin:
  api:
    url: http://localhost:8082
    key: default-key
springdoc:
  swagger-ui:
    disable-swagger-default-url: true
    path: "/swagger-ui.html"
  api-docs:
    path: "/v3/api-docs"
    version: "openapi_3_0"
  show-actuator: false

# API Security Configuration
security:
  api:
    key: default-api-key

management:
  endpoints:
    web:
      exposure:
        include: "health,info,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
