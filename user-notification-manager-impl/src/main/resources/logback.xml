<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Отключение логирования для актуатора -->
    <logger name="org.springframework.boot.actuate" level="OFF" additivity="false" />


    <appender name="OpenTelemetry"
              class="io.opentelemetry.instrumentation.logback.appender.v1_0.OpenTelemetryAppender">
        <captureExperimentalAttributes>true</captureExperimentalAttributes>
        <captureCodeAttributes>true</captureCodeAttributes>
        <captureMarkerAttribute>true</captureMarkerAttribute>
        <captureKeyValuePairAttributes>true</captureKeyValuePairAttributes>
        <captureLoggerContext>true</captureLoggerContext>
    </appender>

    <logger name="uz.uzum.usernotificationmanager" level="DEBUG" />

    <root level="INFO">
<!--        <appender-ref ref="console"/>-->
        <appender-ref ref="OpenTelemetry"/>
    </root>
</configuration>