---
spring:
  datasource:
    url: "${DB_URL}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  liquibase:
    enabled: "${LIQUIBASE_ENABLED:true}"
  rabbitmq:
    host: ${RABBITMQ_HOST}
    port: ${RABBITMQ_PORT}
    username: ${RABBITMQ_USER}
    password: ${RABBITMQ_PASSWORD}
  kafka:
    bootstrap-servers: ${KAFKA_SASL_BOOTSTRAP_SERVERS}
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID}
    security:
      protocol: SASL_SSL
    ssl:
      trust-store-certificates: ${KAFKA_TRUST_STORE_CERT}
      trust-store-type: PEM
    properties:
      security.protocol: SASL_SSL
      sasl.mechanism: PLAIN
      sasl.jaas.config: >
        org.apache.kafka.common.security.plain.PlainLoginModule required
        username="${KAFKA_LOGIN}" password="${KAFKA_PASSWORD}";
      session.timeout.ms: 60000
      metadata.fetch.timeout.ms: 60000
      socket.connection.setup.timeout.ms: 65000
server:
  port: "${SERVER_PORT}"
  instance: "${SERVER_INSTANCE}"
rabbitmq:
  queue: "${RABBITMQ_QUEUE}"
  exchange: "${RABBITMQ_EXCHANGE}"
  routing-key: "${RABBITMQ_ROUTING_KEY}"
kafka:
  topics:
    notifications-update: "${KAFKA_NOTIFICATIONS_UPDATE_TOPIC}"
apelsin:
  api:
    url: "${APELSIN_API_URL}"
    key: "${APELSIN_API_KEY}"
security:
  api:
    key: "${X_API_KEY}"
