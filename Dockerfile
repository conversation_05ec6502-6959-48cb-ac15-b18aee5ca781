FROM eclipse-temurin:21-jre-jammy

RUN addgroup --system spring && adduser --system spring --ingroup spring
USER spring:spring

WORKDIR /opt/app

ARG SPRING_PROFILE=build
ARG SERVER_PORT=8080
ARG DEBUG_PORT=5005
ARG EXTRA_ARGS=""

ENV SPRING_PROFILE=${SPRING_PROFILE} \
    SERVER_PORT=${SERVER_PORT} \
    DEBUG_PORT=${DEBUG_PORT} \
    JAVA_OPTS=${JAVA_OPTS}

COPY ./user-notification-manager-impl/build/libs/*.jar /opt/app/app.jar

HEALTHCHECK --interval=30s --timeout=3s \
    CMD curl -f http://localhost:${SERVER_PORT}/actuator/health || exit 1

EXPOSE ${SERVER_PORT}
EXPOSE ${DEBUG_PORT}

ENTRYPOINT [ "sh", "-c", "java ${JAVA_OPTS} \
    -jar \
    -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:${DEBUG_PORT} \
    app.jar \
    --server.port=${SERVER_PORT} \
    --spring.profiles.active=${SPRING_PROFILE} \
    ${EXTRA_ARGS}" ]
